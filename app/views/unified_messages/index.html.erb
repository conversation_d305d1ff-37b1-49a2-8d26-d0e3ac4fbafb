<% content_for :page_title, t('Messages') %>
<% add_body_class 'ic-no-flex-layout full-width no-page-block' %>

<div id="messages-page-container">
  <!-- Messages Tabs Navigation -->
  <div id="messagesTabs" class="messages-tabs-container">
    <div class="tabs-navigation" role="tablist">
      <button class="messages-tab <%= 'active' if @active_tab == 'inbox' %>" 
              data-tab="inbox"
              role="tab"
              aria-selected="<%= @active_tab == 'inbox' %>"
              aria-controls="tab-content-inbox">
        <%= t('Inbox') %>
        <% if @unread_conversations_count > 0 %>
          <span class="tab-badge" aria-label="<%= t('%{count} unread conversations', count: @unread_conversations_count) %>">
            <%= @unread_conversations_count %>
          </span>
        <% end %>
      </button>
      <button class="messages-tab <%= 'active' if @active_tab == 'chat' %>" 
              data-tab="chat"
              role="tab"
              aria-selected="<%= @active_tab == 'chat' %>"
              aria-controls="tab-content-chat">
        <%= t('Chat') %>
        <% if @unread_chat_count > 0 %>
          <span class="tab-badge" aria-label="<%= t('%{count} unread chat messages', count: @unread_chat_count) %>">
            <%= @unread_chat_count %>
          </span>
        <% end %>
      </button>
    </div>
  </div>

  <!-- Tab Content Areas -->
  <div class="tab-content-container">
    <!-- Inbox Tab Content -->
    <div id="tab-content-inbox" class="tab-content <%= 'active' if @active_tab == 'inbox' %>" role="tabpanel" aria-labelledby="inbox-tab">
      <div id="inbox-content">
        <!-- React Inbox component will be moved here from the hidden content div -->
      </div>
    </div>

    <!-- Chat Tab Content -->
    <div id="tab-content-chat" class="tab-content <%= 'active' if @active_tab == 'chat' %>" role="tabpanel" aria-labelledby="chat-tab">
      <div id="chat-content" class="chat-content-wrapper">
        <!-- Chat UI embedded here -->
        <div class="chat-main-container">
          <div class="chat-main">
            <div class="chat-header">
              <div class="chat-header-user">
                <div class="selected-user-avatar">
                  <img id="selected-user-avatar" src="" alt="" style="display: none;">
                </div>
                <div class="selected-user-info">
                  <h3 id="selected-user-name"><%= t('Select a user to start chatting') %></h3>
                  <span id="selected-user-status" class="user-status-text"></span>
                </div>
              </div>
            </div>
            <div class="chat-messages" id="chat-messages">
              <div class="chat-placeholder">
                <i class="icon-solid icon-message"></i>
                <p><%= t('Select a user to start chatting') %></p>
              </div>
            </div>
            <div class="chat-input-area">
              <form id="chat-form" class="">
                <input type="hidden" id="recipient-id" value="">
                <input type="text" id="chat-input" placeholder="<%= t('Type your message here...') %>" disabled>
                <button type="submit" id="send-button" disabled>
                  <i class="icon-solid icon-arrow-end"></i>
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Chat Sidebar (Users List) -->
        <div class="chat-sidebar-right" id="chat-sidebar">
          <div class="chat-sidebar-header">
            <div class="chat-header-content">
              <i class="icon-solid icon-message chat-icon"></i>
              <h3><%= t('Users') %></h3>
            </div>
          </div>
          <div class="chat-search-area">
            <input type="text" id="chat-search" placeholder="<%= t('Search users...') %>" class="chat-search-input">
          </div>
          <div class="chat-users-list">
            <!-- User list will be populated here via JavaScript -->
            <div class="loading-users">
              <i class="icon-solid icon-refresh icon-spin"></i>
              <p><%= t('Loading users...') %></p>
            </div>
          </div>
          <div class="chat-sidebar-spacer"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Hidden content div for inbox React component to mount to initially -->
<div id="content" style="display: none;"></div>

<script>
  // Pass current user data to JavaScript for chat functionality
  window.chatConfig = {
    currentUserId: <%= @current_user_id %>,
    currentUserName: '<%= j(@current_user_name) %>',
    currentUserAvatar: '<%= j(@current_user_avatar) %>'
  };

  // Debug logging for unified messages page
  console.log('Unified Messages Page: Template loaded');
  console.log('Unified Messages Page: Current URL:', window.location.href);
  console.log('Unified Messages Page: ENV.UNIFIED_MESSAGES_PAGE:', window.ENV?.UNIFIED_MESSAGES_PAGE);

  // Tab switching functionality
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Unified Messages Page: DOMContentLoaded event fired');
    const tabs = document.querySelectorAll('.messages-tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
      tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        const targetTab = this.getAttribute('data-tab');
        
        // Remove active class from all tabs and content
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        
        // Add active class to clicked tab and corresponding content
        this.classList.add('active');
        document.getElementById('tab-content-' + targetTab).classList.add('active');

        // Show/hide chat sidebar based on active tab
        const chatSidebar = document.getElementById('chat-sidebar');
        if (chatSidebar) {
          if (targetTab === 'chat') {
            chatSidebar.style.display = 'flex';
          } else {
            chatSidebar.style.display = 'none';
          }
        }

        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('tab', targetTab);
        window.history.pushState({}, '', url);

        // Load content based on tab
        loadTabContent(targetTab);
        
        // Announce tab change to screen readers
        const tabName = targetTab === 'inbox' ? '<%= j(t('Inbox')) %>' : '<%= j(t('Chat')) %>';
        const announcement = '<%= j(t('Switched to %{tabName} tab')) %>'.replace('%{tabName}', tabName);
        
        // Create and announce to screen readers
        const srAnnouncement = document.createElement('div');
        srAnnouncement.setAttribute('aria-live', 'polite');
        srAnnouncement.setAttribute('aria-atomic', 'true');
        srAnnouncement.className = 'screenreader-only';
        srAnnouncement.textContent = announcement;
        document.body.appendChild(srAnnouncement);
        
        setTimeout(() => {
          document.body.removeChild(srAnnouncement);
        }, 1000);
      });
    });
    
    // Load initial content
    loadTabContent('<%= @active_tab %>');

    // Set initial sidebar visibility
    const chatSidebar = document.getElementById('chat-sidebar');
    if (chatSidebar) {
      if ('<%= @active_tab %>' === 'chat') {
        chatSidebar.style.display = 'flex';
      } else {
        chatSidebar.style.display = 'none';
      }
    }
  });

  function loadTabContent(tab) {
    if (tab === 'inbox') {
      loadInboxContent();
    } else if (tab === 'chat') {
      loadChatContent();
    }
  }

  function loadInboxContent() {
    // The React component will mount directly to the inbox-content div
    // We just need to ensure the container is ready
    const inboxContainer = document.getElementById('inbox-content');
    if (inboxContainer) {
      console.log('Inbox content container ready for React component');
    } else {
      console.log('Inbox content container not found');
    }
  }

  function loadChatContent() {
    // Chat content is already embedded in the template
    // We just need to ensure the chat JavaScript is initialized
    const chatContainer = document.getElementById('chat-content');
    if (chatContainer) {
      console.log('Chat content container ready');

      // Check if chat users list exists and initialize if needed
      const chatUsersList = document.querySelector('.chat-users-list');
      if (chatUsersList && window.chatPageInitialized !== true) {
        console.log('Initializing chat functionality...');

        // Trigger chat initialization if the chat page bundle is loaded
        if (typeof window.initializeChatPage === 'function') {
          window.initializeChatPage();
          window.chatPageInitialized = true;
        } else {
          console.log('Chat page initialization function not found, chat should auto-initialize');
        }
      }
    }
  }
</script>

<%= action_cable_meta_tag %>

<style>
  /* Override main content padding for full-width messages page */
  body.full-width.no-page-block .ic-Layout-contentMain {
    padding: 0;
  }

  #messages-page-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
    height: calc(100vh - 160px); /* Account for header + breadcrumbs */
    min-height: 500px; /* Minimum height for smaller screens */
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .messages-page-header {
    text-align: center;
    margin-bottom: 0;
    padding: 20px;
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    border-bottom: 1px solid #e1e4e8;
  }
  
  .messages-page-title {
    font-size: 28px;
    color: #2D7D32;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }
  
  .messages-page-title i {
    font-size: 32px;
  }
  
  .messages-page-subtitle {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
  
  .messages-tabs-container {
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    margin-bottom: 0;
    border-bottom: 1px solid #e1e4e8;
    flex-shrink: 0; /* Prevent tabs from shrinking */
  }
  
  .tabs-navigation {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .messages-tab {
    flex: 1;
    padding: 16px 24px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .messages-tab:hover {
    background-color: #f5f5f5;
    color: #333;
  }
  
  .messages-tab.active {
    color: #2D7D32;
    background-color: #f8f9fa;
    border-bottom: 3px solid #2D7D32;
  }
  
  .tab-badge {
    background-color: #e74c3c;
    color: white;
    border-radius: 50%;
    min-width: 20px;
    width: auto;
    height: 20px;
    font-size: 11px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1.1;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    padding: 1px 4px 0 4px;
    box-sizing: border-box;
    text-align: center;
  }
  
  .tab-content-container {
    background-color: #f8f9fa;
    border-radius: 0;
    box-shadow: none;
    flex: 1; /* Take remaining space */
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
  
  .tab-content {
    display: none;
    padding: 0;
    height: 100%;
  }

  .tab-content.active {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  #inbox-content, #chat-content {
    height: 100%;
    flex: 1;
    overflow: hidden;
  }
  
  .screenreader-only {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
  }

  /* Chat styles for the chat tab */
  .chat-main-container {
    flex: 1;
    min-height: 400px;
    background-color: white;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .chat-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: white;
    flex: 1;
  }

  .chat-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    flex-shrink: 0;
  }

  .chat-header-user {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .selected-user-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
  }

  .selected-user-info h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }

  .user-status-text {
    font-size: 14px;
    color: #666;
  }

  .chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f8f9fa;
  }

  .chat-placeholder {
    text-align: center;
    color: #666;
    margin-top: 50px;
  }

  .chat-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #ccc;
  }

  .chat-input-area {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: white;
    flex-shrink: 0;
  }

  #chat-form {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  #chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
  }

  #chat-input:focus {
    border-color: #2D7D32;
    box-shadow: 0 0 0 2px rgba(45, 125, 50, 0.1);
  }

  #send-button {
    padding: 12px 16px;
    background-color: #2D7D32;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
  }

  #send-button:hover:not(:disabled) {
    background-color: #1B5E20;
  }

  #send-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  /* Chat Content Wrapper - Flexbox layout for main chat and sidebar */
  .chat-content-wrapper {
    display: flex;
    gap: 2px;
    height: 100%;
    background-color: #f0f0f0;
    padding: 0;
    flex: 1;
    overflow: hidden;
  }

  .chat-main-container {
    flex: 1;
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #e1e4e8;
  }

  /* Chat Sidebar Styles */
  .chat-sidebar-right {
    width: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    height: 100%;
    border: 1px solid #e1e4e8;
  }

  .chat-sidebar-header {
    padding: 15px;
    background-color: #2D7D32;
    color: white;
    border-bottom: 1px solid #e5e5e5;
    flex-shrink: 0;
  }

  .chat-header-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .chat-icon {
    font-size: 18px;
  }

  .chat-sidebar-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .chat-search-area {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    flex-shrink: 0;
  }

  .chat-search-input {
    width: 230px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
  }

  .chat-users-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
  }

  .loading-users {
    text-align: center;
    padding: 20px;
    color: #666;
  }

  .loading-users i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
  }

  .chat-user-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    position: relative;
  }

  .chat-user-item:hover {
    background-color: #f8f9fa;
  }

  .chat-user-item.selected {
    background-color: #e8f5e8;
    border-left: 4px solid #2D7D32;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
    border: 2px solid #e5e5e5;
  }

  .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .user-name {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
  }

  .user-status-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .user-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 1px #e5e5e5;
  }

  .user-status.online {
    background-color: #4CAF50;
  }

  .user-status.offline {
    background-color: #9e9e9e;
  }

  .user-status-text {
    font-size: 12px;
    color: #666;
  }

  /* User unread badge styles - matching navigation badge style */
  .user-unread-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #e74c3c;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    min-width: 20px;
    width: auto;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    z-index: 1000;
    padding: 0 4px;
    box-sizing: border-box;
  }

  .user-unread-badge .unread-count-number {
    color: white;
    font-size: 11px;
    font-weight: bold;
    line-height: 1.1;
    text-align: center;
    display: block;
    margin: 0;
    padding: 1px 0 0 0;
    position: relative;
    top: 1px;
  }

  .chat-sidebar-spacer {
    flex: 0;
  }

  /* Responsive design for smaller screens */
  @media (max-width: 768px) {
    #messages-page-container {
      height: calc(100vh - 140px); /* Slightly less offset on mobile */
      min-height: 400px;
    }

    .chat-content-wrapper {
      flex-direction: column;
      height: 100%;
    }

    .chat-sidebar-right {
      width: 100%;
      order: -1;
      max-height: 200px;
      flex-shrink: 0;
    }

    .chat-main-container {
      flex: 1;
      min-height: 300px;
    }
  }

  /* For very small screens */
  @media (max-height: 600px) {
    #messages-page-container {
      height: calc(100vh - 140px);
      min-height: 400px;
    }
  }
</style>
